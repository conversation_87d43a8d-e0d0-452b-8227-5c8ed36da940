import AppAddress from '@/components/AppAddress';
import { AppTimeDisplay } from '@/components/AppTimeDisplay';
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from '@/libs/broadcast';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import rf from '@/services/RequestFactory';
import { TTrades } from '@/types';
import { formatNumber } from '@/utils/format';
import { getRandomAvatarUrl } from '@/utils/helper';
import { LinkExternalIcon } from '@/assets/icons';
import config from '@/config';
import clsx from 'clsx';
import Image from 'next/image';
import { useCallback, useEffect, useState, useRef } from 'react';
import AppTable, { IColumn } from '../AppTable';

const TransactionList = () => {
  const { coinAddress } = useCoinPageContext();
  const [page, setPage] = useState(1);
  const [tradeData, setTradeData] = useState<TTrades[]>([]);
  const [hasNextPage, setHasNextPage] = useState(true);

  const existingTradeIds = useRef(new Set<string>());

  const handleWhenTradeCreated = useCallback(
    async (event: TBroadcastEvent) => {
      const data = JSON.parse(event?.detail);
      if (data?.tokenAddress === coinAddress) {
        const tradeId = data.id || data.hash;
        if (tradeId && !existingTradeIds.current.has(tradeId)) {
          existingTradeIds.current.add(tradeId);
          setTradeData((prev) => [data, ...prev]);
        }
      }
    },
    [coinAddress],
  );

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.TRADES, handleWhenTradeCreated);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.TRADES, handleWhenTradeCreated);
    };
  }, [handleWhenTradeCreated]);

  const columns: IColumn<TTrades>[] = [
    {
      title: 'Account',
      key: 'maker',
      render: (_, value) => (
        <div>
          <div className="flex items-center gap-8px">
            <Image
              src={getRandomAvatarUrl()}
              alt="avatar"
              width={32}
              height={32}
              className="object-cover w-32px h-32px rounded-full"
            />
            <div className="flex items-center gap-8px">
              <AppAddress address={value?.maker} />
              <a
                href={`${config.explorerUrl}/address/${value?.maker}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-white-500 hover:text-brand-500 transition-colors"
              >
                <LinkExternalIcon className="w-3 h-3" />
              </a>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Type',
      key: 'tradeType',
      render: (value) => (
        <p
          className={clsx(
            'text-body-sm font-normal',
            value === 'BUY' ? 'text-green-500' : 'text-red-500',
          )}
        >
          {value === 'BUY' ? 'Buy' : 'Sell'}
        </p>
      ),
    },
    {
      title: 'HYPE',
      key: 'quoteAmount',
      render: (_, value) => (
        <p className="text-body-sm font-normal">
          {formatNumber(value.quoteAmount, 9, '0')}
        </p>
      ),
    },
    {
      title: 'Token',
      key: 'baseAmount',
      render: (_, value) => (
        <p className="text-body-sm font-normal">
          {formatNumber(value.baseAmount, 9, '0')}
        </p>
      ),
    },
    {
      title: 'Date',
      key: 'timestamp',
      render: (_, value) => (
        <div className="text-body-sm font-normal">
          <AppTimeDisplay timestamp={value?.timestamp} isAgo />
        </div>
      ),
    },
    {
      title: 'Transaction',
      key: 'hash',
      render: (_, value) => (
        <div className="flex items-center gap-8px">
          <AppAddress address={value.hash} />
          <a
            href={`${config.explorerUrl}/tx/${value.hash}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-white-500 hover:text-brand-500 transition-colors"
          >
            <LinkExternalIcon className="w-3 h-3" />
          </a>
        </div>
      ),
    },
  ];

  const handleNextPage = useCallback(() => {
    if (hasNextPage) {
      setPage((prev) => prev + 1);
    }
  }, [hasNextPage]);

  const handleGetTrades = useCallback(async () => {
    if (!coinAddress) return;
    try {
      await rf
        .getRequest('TradeRequest')
        .getTrades({
          page,
          limit: 20,
          tokenAddress: coinAddress || '',
        })
        .then((result) => {
          if (!!result) {
            const newDocs = result?.docs.map((item: any) => ({
              ...item,
              user: {
                ...item.user,
                fallbackLogoUrl: getRandomAvatarUrl(),
              },
            }));

            if (newDocs) {
              const filteredDocs = newDocs.filter((item: TTrades) => {
                const tradeId = item.id || item.hash;
                if (tradeId && !existingTradeIds.current.has(tradeId)) {
                  existingTradeIds.current.add(tradeId);
                  return true;
                }
                return false;
              });

              setTradeData((prev) =>
                page === 1 ? filteredDocs : [...prev, ...filteredDocs],
              );
            } else {
              setTradeData([]);
            }

            if (page >= result?.totalPages) {
              setHasNextPage(false);
            }
          }
        });
    } catch (err) {
      console.error(err);
      setTradeData([]);
      existingTradeIds.current.clear();
    }
  }, [page, coinAddress]);

  useEffect(() => {
    handleGetTrades();
  }, [handleGetTrades]);

  useEffect(() => {
    if (coinAddress) {
      setTradeData([]);
      setPage(1);
      setHasNextPage(true);
      existingTradeIds.current.clear();
    }
  }, [coinAddress]);

  return (
    <div className="px-4 overflow-x-auto">
      <AppTable
        columns={columns}
        data={tradeData}
        loadMore={handleNextPage}
        noDataMessage="No Transaction"
        keyExtractor={(item: TTrades) =>
          item.id || item.hash || `${item.maker}-${item.timestamp}`
        }
      />
    </div>
  );
};

export default TransactionList;
