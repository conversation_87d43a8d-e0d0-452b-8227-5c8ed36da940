import { useIntersectionObserver } from '@/hooks';
import clsx from 'clsx';
import React, { forwardRef, memo } from 'react';
export interface IColumn<T> {
  key: keyof T;
  title: string | React.ReactNode;
  render?: (value: T[keyof T], rowData: T, index: number) => React.ReactNode;
  headerClassName?: string;
  rowClassName?: string;
}
export interface AppTableProps<T> {
  data: T[];
  columns: Array<IColumn<T>>;
  loadMore?: () => void;
  noDataMessage?: string;
  keyExtractor?: (item: T, index: number) => string;
}
const AppTable = memo(
  forwardRef(
    <T,>(
      {
        columns,
        data,
        loadMore,
        noDataMessage,
        keyExtractor,
        ...props
      }: AppTableProps<T>,
      ref: React.Ref<HTMLTableElement>,
    ) => {
      const { setEl } = useIntersectionObserver({ loadMore });
      return (
        <table ref={ref} {...props} className="w-full">
          <thead>
            <tr className="border-b border-white-50">
              {columns.map((column, index) => (
                <th
                  key={index}
                  className={clsx(
                    'text-body-sm text-white-500 p-8px',
                    column.headerClassName,
                  )}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.length <= 0 ? (
              <tr>
                <td colSpan={columns.length} className="h-[200px]">
                  <p className="text-body-sm text-center">
                    {noDataMessage || 'No Data'}
                  </p>
                </td>
              </tr>
            ) : (
              <>
                {data.map((item, rowIndex) => {
                  const uniqueKey = keyExtractor
                    ? keyExtractor(item, rowIndex)
                    : rowIndex;
                  return (
                    <tr key={uniqueKey} className="border-b border-white-50">
                      {columns.map((column, colIndex) => (
                        <td
                          key={colIndex}
                          className={clsx(column.rowClassName)}
                          ref={(el) => {
                            if (rowIndex !== data.length - 1) return;
                            setEl(el);
                          }}
                        >
                          {column.render ? (
                            column.render(item[column.key], item, rowIndex)
                          ) : (
                            <>{item[column.key] as string}</>
                          )}
                        </td>
                      ))}
                    </tr>
                  );
                })}
              </>
            )}
          </tbody>
        </table>
      );
    },
  ),
) as <T>(
  props: AppTableProps<T> & { ref?: React.Ref<HTMLTableElement> },
) => JSX.Element;
export default AppTable;
