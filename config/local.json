{"apiUrl": "https://dev-api-printmeme.sotalabs.io/api/v1", "recaptchaSiteKey": "6Ld5bsAqAAAAAH4h2aBEaoYy-qK5e-Wsc00n05Sp", "feeRecipient": "0x742d35Cc6634C0532925a3b8D4C9db96C4b5Da5e", "explorerUrl": "https://app.hyperliquid.xyz/explorer", "network": "hyperliquid-testnet", "endpoints": {"ws": "https://dev-ws-printmeme.sotalabs.io"}, "link": {"twitter": "/", "telegram": "/"}, "hyperliquidRpcUrl": "https://api.hyperliquid.xyz/evm", "chainId": 999, "privyAppId": "cm747q3md02i7108em0o2anzx", "platformFeeRate": "0.01", "kingTheHill": "0.02", "bondingConfig": {"initVirtualTokenReserves": "10666666666666666666666666", "remainTokenReserves": "2666666666666666666666666", "targetRaise": "30000000000000000", "feeCreatePool": "1000000000000000", "swapFeeRate": "0.01"}, "contracts": {"multicallAddress": "******************************************", "mainTokenAddress": "******************************************", "launchpadAddress": "******************************************", "tokenLockAddress": "******************************************", "swapRouterV3Address": "******************************************", "wethAddress": "******************************************"}}